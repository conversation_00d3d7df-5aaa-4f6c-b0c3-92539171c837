"use client";

/**
 * Context Provider cho xác thực người dùng
 * Quản lý trạng thái người dùng và cung cấp các hàm xác thực
 */
import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";
import { AuthResponse, SignInData, SignUpData, User } from "shared-types";
import { createClient } from "../supabase/client";

// Định nghĩa kiểu dữ liệu cho context
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signUp: (data: SignUpData) => Promise<AuthResponse>;
  signIn: (data: SignInData) => Promise<AuthResponse>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

// Tạo context với giá trị mặc định
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook để sử dụng context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Props cho AuthProvider
interface AuthProviderProps {
  children: ReactNode;
}

// AuthProvider component
export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const supabase = createClient();

  // Hàm để lấy thông tin người dùng hiện tại
  const refreshUser = async () => {
    try {
      setIsLoading(true);
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        // Lấy thông tin profile từ bảng profiles
        const { data: profile } = await supabase
          .from("profiles")
          .select("full_name, avatar_url")
          .eq("id", user.id)
          .single();

        setUser({
          id: user.id,
          email: user.email || "",
          full_name: profile?.full_name,
          avatar_url: profile?.avatar_url,
          created_at: user.created_at || new Date().toISOString(),
        });
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error("Error refreshing user:", error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Đăng ký người dùng mới
  const signUp = async (data: SignUpData): Promise<AuthResponse> => {
    try {
      const { email, password, full_name } = data;

      // Đăng ký người dùng mới với Supabase Auth
      const { data: authData, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name,
          },
        },
      });

      if (error) {
        return { user: null, error: error.message };
      }

      if (authData.user) {
        // Tạo profile cho người dùng mới
        const { error: profileError } = await supabase.from("profiles").insert([
          {
            id: authData.user.id,
            full_name,
            updated_at: new Date().toISOString(),
          },
        ]);

        if (profileError) {
          console.error("Error creating profile:", profileError);
          return {
            user: null,
            error: "Đăng ký thành công nhưng không thể tạo hồ sơ người dùng.",
          };
        }

        // Cập nhật trạng thái người dùng
        await refreshUser();

        return {
          user: {
            id: authData.user.id,
            email: authData.user.email || "",
            full_name,
            created_at: authData.user.created_at || new Date().toISOString(),
          },
          error: null,
        };
      }

      return { user: null, error: "Đăng ký không thành công." };
    } catch (error) {
      console.error("Error signing up:", error);
      return { user: null, error: "Đã xảy ra lỗi khi đăng ký." };
    }
  };

  // Đăng nhập người dùng
  const signIn = async (data: SignInData): Promise<AuthResponse> => {
    try {
      const { email, password } = data;

      // Đăng nhập với Supabase Auth
      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { user: null, error: error.message };
      }

      if (authData.user) {
        // Cập nhật trạng thái người dùng
        await refreshUser();

        return {
          user: {
            id: authData.user.id,
            email: authData.user.email || "",
            created_at: authData.user.created_at || new Date().toISOString(),
          },
          error: null,
        };
      }

      return { user: null, error: "Đăng nhập không thành công." };
    } catch (error) {
      console.error("Error signing in:", error);
      return { user: null, error: "Đã xảy ra lỗi khi đăng nhập." };
    }
  };

  // Đăng xuất người dùng
  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  // Kiểm tra trạng thái xác thực khi component được mount
  useEffect(() => {
    const initializeAuth = async () => {
      await refreshUser();

      // Lắng nghe sự thay đổi trạng thái xác thực
      const {
        data: { subscription },
      } = await supabase.auth.onAuthStateChange(async event => {
        if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
          await refreshUser();
        } else if (event === "SIGNED_OUT") {
          setUser(null);
        }
      });

      return () => {
        subscription.unsubscribe();
      };
    };

    initializeAuth();
  }, []);

  // Giá trị được cung cấp bởi context
  const value = {
    user,
    isLoading,
    signUp,
    signIn,
    signOut,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
