/**
 * Supabase client singleton
 * 
 * File này tạo một instance duy nhất của Supabase client để sử dụng trong toàn bộ ứng dụng.
 * Sử dụng createBrowserClient để tạo client phía trình duyệt.
 */
import { createBrowserClient } from '@supabase/ssr'

// Khởi tạo Supabase client với URL và Anon Key từ biến môi trường
export const createClient = () => {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
