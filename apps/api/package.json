{"name": "api", "version": "0.1.0", "private": true, "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext .ts", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "@types/node": "^20.11.30", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-config-prettier": "^9.0.0", "ts-node-dev": "^2.0.0", "typescript": "^5.4.5"}}