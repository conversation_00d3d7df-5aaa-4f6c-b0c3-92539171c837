---
docType: story
epic: 1
story: 1
title: Thi<PERSON>t lập môi trường phát triển và Monorepo
status: Ready for Review
---

# Story 1.1: Thiết lập môi trường phát triển và Monorepo

**As a** developer, **I want to** set up the monorepo structure with pnpm, **so that I can** manage frontend and backend code efficiently.

## Acceptance Criteria

- **1.1.1:** <PERSON><PERSON><PERSON> đư<PERSON>c khởi tạo với pnpm.
- **1.1.2:** Các project frontend (Next.js) và backend (Node.js/Express) được tạo trong monorepo.
- **1.1.3:** C<PERSON><PERSON> hình TypeScript được thiết lập cho cả frontend và backend.
- **1.1.4:** Các script cơ bản để chạy dev server và build được cấu hình.

## QA Results

### Review Date: 2025-08-21

### Reviewed By: <PERSON> (Test Architect)

### Code Quality Assessment

Cấu trúc monorepo đã được thiết lập đúng theo yêu cầu và tuân thủ các tiêu chuẩn kỹ thuật. Tất cả các thành phần cần thiết đã được cấu hình đầy đủ và hoạt động chính xác.

### Refactoring Performed

Không cần thực hiện refactoring vì cấu trúc dự án đã được thiết lập tốt.

### Compliance Check

- Coding Standards: ✓ (Tuân thủ các tiêu chuẩn trong docs/architecture/coding-standards.md)
- Project Structure: ✓ (Đúng cấu trúc theo docs/architecture/unified-project-structure.md)
- Testing Strategy: ✓ (Đã thiết lập các framework testing theo docs/architecture/testing-strategy.md)
- All ACs Met: ✓ (Tất cả các AC đều được triển khai đầy đủ)

### Improvements Checklist

- [ ] Cân nhắc thêm các script tự động hóa cho quy trình CI/CD
- [ ] Cập nhật các phụ thuộc khi có phiên bản mới

### Security Review

Không phát hiện vấn đề bảo mật trong thiết lập cơ bản của dự án.

### Performance Considerations

Cấu trúc monorepo được thiết lập tối ưu, không có vấn đề về hiệu suất.

### Files Modified During Review

Không có file nào được sửa đổi trong quá trình review.

### Gate Status

Gate: PASS → docs/qa/gates/1.1-thiet-lap-moi-truong-phat-trien-va-monorepo.yml

### Recommended Status

✓ Ready for Done
(Story owner decides final status)

## Dev Notes

### Previous Story Insights

- This is the first story, no previous insights.

### Data Models

- No specific data models are being implemented in this story. [Source: architecture/data-models.md]

### API Specifications

- No API endpoints are being implemented in this story. [Source: architecture/api-specification.md]

### Component Specifications

- No UI components are being built in this story. [Source: architecture/components.md]

### File Locations

- The project structure should be a monorepo managed by pnpm.
- The root directory will contain `pnpm-workspace.yaml`.
- Applications will be located in the `apps/` directory.
- Shared packages (UI components, types, configs) will be in the `packages/` directory.
- **`apps/web`**: Next.js 13 frontend application.
- **`apps/api`**: Node.js/Express backend application.
- **`packages/shared-types`**: For sharing TypeScript types between `web` and `api`.
- **`packages/ui`**: Shared React components (based on shadcn/ui).
- **`packages/config`**: Shared configurations (e.g., ESLint, TypeScript).
- [Source: architecture/unified-project-structure.md]

### Testing Requirements

- While this story is primarily about setup, the testing frameworks should be installed as part of the initial dependency setup.
- **Frameworks to install**: Jest, React Testing Library (for frontend), Supertest (for backend), Playwright (for E2E).
- Basic test configurations should be created for both `apps/web` and `apps/api`.
- [Source: architecture/testing-strategy.md]

### Technical Constraints & Guidance

- **Package Manager**: Use `pnpm` for workspace management. [Source: architecture/development-workflow.md]
- **Node.js Version**: Use Node.js v20+. [Source: architecture/development-workflow.md]
- **Frontend**:
  - Framework: Next.js 13.x
  - Language: TypeScript ~5.4.5
  - UI: shadcn/ui, Tailwind CSS
  - Bundler: Webpack (default for Next.js 13)
- **Backend**:
  - Framework: Node.js / Express ~4.19.2
  - Language: TypeScript ~5.4.5
- **TypeScript**: A base `tsconfig.json` should be created in the `packages/config` directory and extended by the applications in the `apps` directory to ensure consistency. [Source: architecture/coding-standards.md]
- **Git Workflow**: Use Feature Branch Workflow with Pull Requests and Code Review. [Source: architecture/development-workflow.md]

## Tasks / Subtasks

1.  **Initialize Project and pnpm Workspace (AC: 1.1.1)**

    - [x] Initialize a new git repository.
    - [x] Create a `pnpm-workspace.yaml` file in the root.
    - [x] Create the directory structure: `apps/`, `packages/`. [Source: architecture/unified-project-structure.md]

2.  **Scaffold Frontend Application (AC: 1.1.2)**

    - [x] Inside the `apps/` directory, create a new Next.js 13 project named `web`.
    - [x] Ensure the project is initialized with TypeScript.
    - [x] Install core frontend dependencies: `react`, `react-dom`, `next`, `typescript`, `@types/react`, `@types/node`. [Source: architecture/tech-stack.md]
    - [x] Install and configure Tailwind CSS. [Source: architecture/tech-stack.md]
    - [x] Install shadcn/ui and its dependencies. [Source: architecture/tech-stack.md]

3.  **Scaffold Backend Application (AC: 1.1.2)**

    - [x] Inside the `apps/` directory, create a new Node.js project named `api`.
    - [x] Initialize it with `pnpm init`.
    - [x] Install core backend dependencies: `express`, `typescript`, `@types/express`, `ts-node`. [Source: architecture/tech-stack.md]
    - [x] Create a basic Express server entry point (e.g., `src/index.ts`).

4.  **Configure Shared TypeScript and Linting (AC: 1.1.3)**

    - [x] Create a `packages/config` directory.
    - [x] Create a base `tsconfig.json` in `packages/config` to be shared.
    - [x] Configure `apps/web/tsconfig.json` and `apps/api/tsconfig.json` to extend the base configuration.
    - [x] Set up shared ESLint/Prettier configurations in `packages/config`.

5.  **Create Development Scripts (AC: 1.1.4)**

    - [x] In the root `package.json`, add scripts to run the `web` and `api` dev servers concurrently (e.g., using `pnpm --parallel`).
    - [x] Add build scripts for both `web` and `api` applications.
    - [x] Ensure `pnpm install` correctly installs dependencies for all workspaces.

6.  **Setup Initial Testing Frameworks**
    - [x] Add Jest, React Testing Library to `apps/web` and create a sample component test.
    - [x] Add Jest, Supertest to `apps/api` and create a sample endpoint test.
    - [x] [Source: architecture/testing-strategy.md]

## Dev Agent Record

### Agent Model Used
Cascade

### Debug Log References
Không có vấn đề nào được phát hiện trong quá trình triển khai.

### Completion Notes
- Đã thiết lập thành công cấu trúc monorepo với pnpm workspace
- Đã cấu hình đầy đủ ứng dụng frontend (Next.js) và backend (Express)
- Đã thiết lập cấu hình TypeScript chung và riêng cho từng ứng dụng
- Đã cấu hình các script phát triển và build
- Đã thiết lập các framework testing cơ bản

### File List
- `/pnpm-workspace.yaml`
- `/package.json`
- `/apps/web/**/*`
- `/apps/api/**/*`
- `/packages/config/tsconfig.base.json`
- `/packages/config/eslint-preset.js`

### Change Log
- Đã kiểm tra và xác nhận tất cả các yêu cầu của story đã được triển khai
- Đã cập nhật trạng thái các nhiệm vụ trong story
