/**
 * <PERSON><PERSON><PERSON> nghĩa các kiểu dữ liệu dùng chung trong toàn bộ ứng dụng
 */

/**
 * Interface đại diện cho thông tin người dùng
 * <PERSON><PERSON><PERSON><PERSON> sử dụng trong cả frontend và backend
 */
export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
}

/**
 * Interface đại diện cho hồ sơ người dùng
 * Tương ứng với bảng profiles trong Supabase
 */
export interface Profile {
  id: string;
  full_name?: string;
  avatar_url?: string;
  updated_at?: string;
}

/**
 * Interface đại diện cho dữ liệu đăng ký người dùng
 */
export interface SignUpData {
  email: string;
  password: string;
  full_name?: string;
}

/**
 * Interface đại diện cho dữ liệu đăng nhập
 */
export interface SignInData {
  email: string;
  password: string;
}

/**
 * Interface đại diện cho phản hồi xác thực
 */
export interface AuthResponse {
  user: User | null;
  error: string | null;
}
