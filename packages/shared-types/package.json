{"name": "shared-types", "version": "0.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "dev": "tsup src/index.ts --format esm,cjs --dts --watch", "lint": "eslint \"src/**/*.ts\"", "clean": "rm -rf dist"}, "devDependencies": {"eslint": "^8.49.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-config-prettier": "^9.0.0", "@shoe-page/config": "workspace:*", "tsup": "^7.2.0", "typescript": "^5.4.5"}}