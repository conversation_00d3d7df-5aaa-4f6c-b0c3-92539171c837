# 🧹 Cleanup Scripts - Hướng dẫn sử dụng

## 📋 Tổng quan

Dự án này đã được cấu hình với các script để quản lý và dọn dẹp các file/folder tạm thời một cách nhanh chóng và hiệu quả.

## 🚀 Các Script có sẵn

### 1. **Xóa Node Modules**
```bash
pnpm run clean:node-modules
```
- ✅ Xóa tất cả thư mục `node_modules` trong toàn bộ workspace
- ✅ Bao gồm cả node_modules trong packages con và apps
- ✅ An toàn và nhanh chóng

### 2. **Xóa Build Outputs**
```bash
pnpm run clean:dist
```
- ✅ Xóa tất cả thư mục `dist/` và `build/`
- ✅ Dọn dẹp các file build cũ
- ✅ Giải phóng dung lượng ổ cứng

### 3. **<PERSON><PERSON><PERSON>**
```bash
pnpm run clean:cache
```
- ✅ <PERSON><PERSON>a thư mục `.turbo/` (Turborepo cache)
- ✅ Xó<PERSON> thư mục `.next/` (Next.js cache)
- ✅ Dọn dẹp cache build

### 4. **Dọn dẹp toàn bộ**
```bash
pnpm run clean
```
- ✅ Chạy cả `clean:node-modules` và `clean:dist`
- ✅ Dọn dẹp toàn diện

### 5. **Cài đặt mới hoàn toàn**
```bash
pnpm run fresh-install
```
- ✅ Dọn dẹp toàn bộ (clean)
- ✅ Cài đặt lại dependencies
- ✅ Khởi tạo môi trường sạch

## 🔧 Khi nào sử dụng?

### `clean:node-modules`
- 🔄 Khi gặp lỗi dependency conflicts
- 🔄 Trước khi switch branch có thay đổi package.json
- 🔄 Khi muốn giải phóng dung lượng nhanh

### `clean:dist`
- 🔄 Trước khi build production
- 🔄 Khi gặp lỗi build cache
- 🔄 Sau khi thay đổi cấu hình build

### `fresh-install`
- 🔄 Khi setup môi trường mới
- 🔄 Khi gặp lỗi không xác định
- 🔄 Trước khi deploy production

## ⚡ Ví dụ sử dụng

```bash
# Tình huống 1: Gặp lỗi dependency
pnpm run clean:node-modules
pnpm install

# Tình huống 2: Build bị lỗi
pnpm run clean:dist
pnpm run build

# Tình huống 3: Reset hoàn toàn
pnpm run fresh-install

# Tình huống 4: Dọn dẹp trước khi commit
pnpm run clean
```

## 🛡️ An toàn

- ✅ **Git Safe**: Tất cả node_modules đã được cấu hình ignore trong `.gitignore`
- ✅ **Workspace Safe**: Script chỉ xóa các thư mục tạm thời, không ảnh hưởng source code
- ✅ **Cross-platform**: Hoạt động trên macOS, Linux, và Windows

## 📁 Cấu trúc .gitignore

```gitignore
# Dependencies
node_modules/

# Build outputs
dist/
build/
.turbo/

# Cache
.next/

# Package managers
pnpm-lock.yaml
yarn.lock
package-lock.json
```

## 💡 Tips

1. **Trước khi switch branch**: `pnpm run clean:node-modules`
2. **Trước khi build production**: `pnpm run clean`
3. **Khi gặp lỗi lạ**: `pnpm run fresh-install`
4. **Tiết kiệm dung lượng**: Chạy `clean` định kỳ

---

*Được tạo để tối ưu hóa workflow development* 🚀
