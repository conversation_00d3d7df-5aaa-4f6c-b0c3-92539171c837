{"name": "shoe-page-k<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "pnpm run --parallel dev", "dev:web": "pnpm --filter web dev", "dev:api": "pnpm --filter api dev", "build": "pnpm run --parallel build", "build:web": "pnpm --filter web build", "build:api": "pnpm --filter api build", "start": "pnpm --filter api start", "lint": "pnpm run --parallel lint", "test": "pnpm run --parallel test", "clean": "pnpm run clean:node-modules && pnpm run clean:dist", "clean:node-modules": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' + && echo 'All node_modules deleted'", "clean:dist": "find . -name 'dist' -type d -prune -exec rm -rf '{}' + && find . -name 'build' -type d -prune -exec rm -rf '{}' + && echo 'All dist/build folders deleted'", "clean:cache": "find . -name '.turbo' -type d -prune -exec rm -rf '{}' + && find . -name '.next' -type d -prune -exec rm -rf '{}' + && echo 'All cache folders deleted'", "fresh-install": "pnpm run clean && pnpm install && echo 'Fresh installation completed'"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.6.0"}